
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				// Modern OKLCH-based theme colors
				border: 'oklch(var(--border))',
				input: 'oklch(var(--input))',
				ring: 'oklch(var(--ring))',
				background: 'oklch(var(--background))',
				foreground: 'oklch(var(--foreground))',
				primary: {
					DEFAULT: 'oklch(var(--primary))',
					foreground: 'oklch(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'oklch(var(--secondary))',
					foreground: 'oklch(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'oklch(var(--destructive))',
					foreground: 'oklch(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'oklch(var(--muted))',
					foreground: 'oklch(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'oklch(var(--accent))',
					foreground: 'oklch(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'oklch(var(--popover))',
					foreground: 'oklch(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'oklch(var(--card))',
					foreground: 'oklch(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'oklch(var(--sidebar-background))',
					foreground: 'oklch(var(--sidebar-foreground))',
					primary: 'oklch(var(--sidebar-primary))',
					'primary-foreground': 'oklch(var(--sidebar-primary-foreground))',
					accent: 'oklch(var(--sidebar-accent))',
					'accent-foreground': 'oklch(var(--sidebar-accent-foreground))',
					border: 'oklch(var(--sidebar-border))',
					ring: 'oklch(var(--sidebar-ring))'
				},
				// Zenith Flow Professional OKLCH Color Palette - 2024-2025
				'zenith-blue': {
					50: 'oklch(0.97 0.014 254.604)',
					100: 'oklch(0.932 0.032 255.585)',
					200: 'oklch(0.882 0.059 254.128)',
					300: 'oklch(0.809 0.105 251.813)',
					400: 'oklch(0.707 0.165 254.624)',
					500: 'oklch(0.623 0.214 259.815)', // Main brand color
					600: 'oklch(0.546 0.245 262.881)',
					700: 'oklch(0.488 0.243 264.376)',
					800: 'oklch(0.424 0.199 265.638)',
					900: 'oklch(0.379 0.146 265.522)'
				},
				'zenith-gray': {
					50: 'oklch(0.985 0.002 247.839)',
					100: 'oklch(0.967 0.003 264.542)',
					200: 'oklch(0.928 0.006 264.531)',
					300: 'oklch(0.872 0.01 258.338)',
					400: 'oklch(0.707 0.022 261.325)',
					500: 'oklch(0.554 0.046 257.417)', // Perfect mid-tone
					600: 'oklch(0.446 0.043 257.281)',
					700: 'oklch(0.372 0.044 257.287)',
					800: 'oklch(0.279 0.041 260.031)',
					900: 'oklch(0.208 0.042 265.755)'
				},
				'zenith-success': {
					50: 'oklch(0.977 0.013 236.62)',
					100: 'oklch(0.951 0.026 236.824)',
					200: 'oklch(0.901 0.058 230.902)',
					300: 'oklch(0.828 0.111 230.318)',
					400: 'oklch(0.746 0.16 232.661)',
					500: 'oklch(0.715 0.143 215.221)', // Success green
					600: 'oklch(0.588 0.158 241.966)',
					700: 'oklch(0.5 0.134 242.749)',
					800: 'oklch(0.443 0.11 240.79)',
					900: 'oklch(0.391 0.09 240.876)'
				},
				'zenith-warning': {
					50: 'oklch(0.977 0.014 308.299)',
					100: 'oklch(0.946 0.033 307.174)',
					200: 'oklch(0.902 0.063 306.703)',
					300: 'oklch(0.827 0.119 306.383)',
					400: 'oklch(0.714 0.203 305.504)',
					500: 'oklch(0.746 0.16 232.661)', // Professional amber
					600: 'oklch(0.558 0.288 302.321)',
					700: 'oklch(0.496 0.265 301.924)',
					800: 'oklch(0.438 0.218 303.724)',
					900: 'oklch(0.381 0.176 304.987)'
				},
				'zenith-error': {
					50: 'oklch(0.971 0.013 17.38)',
					100: 'oklch(0.936 0.032 17.717)',
					200: 'oklch(0.885 0.062 18.334)',
					300: 'oklch(0.808 0.114 19.571)',
					400: 'oklch(0.704 0.191 22.216)',
					500: 'oklch(0.637 0.237 25.331)', // Refined red
					600: 'oklch(0.577 0.245 27.325)',
					700: 'oklch(0.505 0.213 27.518)',
					800: 'oklch(0.444 0.177 26.899)',
					900: 'oklch(0.396 0.141 25.723)'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' }
				},
				'fade-in': {
					'0%': { opacity: '0', transform: 'translateY(10px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-up': {
					'0%': { opacity: '0', transform: 'translateY(20px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'scale-in': {
					'0%': { opacity: '0', transform: 'scale(0.9)' },
					'100%': { opacity: '1', transform: 'scale(1)' }
				},
				'pulse-soft': {
					'0%, 100%': { opacity: '1' },
					'50%': { opacity: '0.8' }
				},
				'bounce-gentle': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-4px)' }
				},
				'zenith-float': {
					'0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
					'50%': { transform: 'translateY(-10px) rotate(3deg)' }
				},
				'zenith-glow': {
					'0%, 100%': { boxShadow: '0 0 5px oklch(var(--primary) / 0.3)' },
					'50%': { boxShadow: '0 0 20px oklch(var(--primary) / 0.5), 0 0 30px oklch(var(--primary) / 0.3)' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'slide-up': 'slide-up 0.4s ease-out',
				'scale-in': 'scale-in 0.2s ease-out',
				'pulse-soft': 'pulse-soft 2s ease-in-out infinite',
				'bounce-gentle': 'bounce-gentle 2s ease-in-out infinite',
				'zenith-float': 'zenith-float 6s ease-in-out infinite',
				'zenith-glow': 'zenith-glow 3s ease-in-out infinite'
			},
			backgroundImage: {
				'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
				'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
				'zenith-gradient': 'linear-gradient(135deg, oklch(var(--primary)) 0%, oklch(var(--primary) / 0.9) 100%)',
				'zenith-gradient-soft': 'linear-gradient(135deg, oklch(var(--primary) / 0.1) 0%, oklch(var(--primary) / 0.05) 100%)'
			},
			boxShadow: {
				'zenith': '0 4px 14px 0 oklch(var(--primary) / 0.15)',
				'zenith-lg': '0 10px 25px -3px oklch(var(--primary) / 0.2), 0 4px 6px -2px oklch(var(--primary) / 0.1)',
				'card-hover': '0 8px 30px oklch(var(--foreground) / 0.08)'
			},
			fontFamily: {
				'arabic': ['Tajawal', 'Cairo', 'Amiri', 'sans-serif'],
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
