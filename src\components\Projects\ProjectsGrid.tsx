
import React, { useState } from 'react';
import { Calendar, Users, TrendingUp, Plus, Loader2, AlertCircle, Edit, Trash2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';
import { useProjects, useProjectMutations } from '@/hooks/useData';
import { Project } from '@/lib/database';
import ProjectForm from './ProjectForm';

const ProjectsGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { projectStatus } = useColors();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);

  // Use real data hooks
  const { data: projects, loading, error, refetch } = useProjects();
  const { updateProject, deleteProject, loading: mutationLoading } = useProjectMutations();

  const handleDeleteProject = async (project: Project) => {
    if (window.confirm(t('projects.confirmDelete'))) {
      try {
        await deleteProject(project.id);
        refetch(); // Refresh the data
      } catch (error) {
        console.error('Failed to delete project:', error);
      }
    }
  };

  const handleFormSuccess = () => {
    refetch();
    setShowCreateForm(false);
    setEditingProject(null);
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12 text-destructive">
          <AlertCircle className="w-8 h-8 mr-2" />
          <span>{t('common.error')}: {error.message}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('projects.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('projects.subtitle')}</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200"
        >
          <Plus className="w-5 h-5" />
          {t('projects.newProject')}
        </button>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {!projects || projects.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <div className="text-muted-foreground mb-4">
              {t('projects.noProjects')}
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('projects.createFirst')}
            </button>
          </div>
        ) : (
          projects.map((project) => (
            <div
              key={project.id}
              className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 group"
            >
              {/* Project Header */}
              <div className="mb-4">
                <div className={cn(
                  "flex items-start justify-between mb-3",
                  isRTL && "flex-row-reverse"
                )}>
                  <div className={cn(
                    `w-3 h-3 rounded-full bg-gradient-to-r ${project.color}`
                  )}></div>
                  <span className={cn(
                    "text-xs px-2 py-1 rounded-full font-medium",
                    project.status === 'active'
                      ? projectStatus.active.className
                      : project.status === 'completed'
                      ? projectStatus.completed.className
                      : projectStatus.pending.className
                  )}>
                    {t(`projects.status.${project.status}`)}
                  </span>
                </div>

                <h3 className={cn(
                  "text-lg font-semibold mb-2 group-hover:text-primary transition-colors",
                  isRTL && "text-right"
                )}>
                  {project.title}
                </h3>

                <p className={cn(
                  "text-muted-foreground text-sm",
                  isRTL && "text-right"
                )}>
                  {project.description}
                </p>
              </div>

            {/* Progress */}
            <div className="mb-4">
              <div className={cn(
                "flex items-center justify-between text-sm mb-2",
                isRTL && "flex-row-reverse"
              )}>
                <span className="text-muted-foreground">{t('projects.progress')}</span>
                <span className="font-medium">{project.progress}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    `h-full rounded-full bg-gradient-to-r ${project.color} transition-all duration-500`
                  )}
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
            </div>

              {/* Stats */}
              <div className={cn(
                "flex items-center justify-between text-sm text-muted-foreground",
                isRTL && "flex-row-reverse"
              )}>
                <div className={cn(
                  "flex items-center gap-1",
                  isRTL && "flex-row-reverse"
                )}>
                  <Users className="w-4 h-4" />
                  <span>{project.team?.length || 0} {t('projects.members')}</span>
                </div>

                <div className={cn(
                  "flex items-center gap-1",
                  isRTL && "flex-row-reverse"
                )}>
                  <TrendingUp className="w-4 h-4" />
                  <span>{project.tasks?.length || 0} {t('projects.tasks')}</span>
                </div>

                {project.deadline && (
                  <div className={cn(
                    "flex items-center gap-1",
                    isRTL && "flex-row-reverse"
                  )}>
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(project.deadline).toLocaleDateString()}</span>
                  </div>
                )}

                {/* Action buttons */}
                <div className="flex items-center gap-2 mt-4 pt-4 border-t border-border">
                  <button
                    onClick={() => setEditingProject(project)}
                    className="flex items-center gap-1 px-3 py-1.5 text-sm bg-muted hover:bg-muted/80 rounded-lg transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                    {t('common.edit')}
                  </button>
                  <button
                    onClick={() => handleDeleteProject(project)}
                    className="flex items-center gap-1 px-3 py-1.5 text-sm bg-destructive/10 text-destructive hover:bg-destructive/20 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                    {t('common.delete')}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Project Form Modal */}
      <ProjectForm
        isOpen={showCreateForm || !!editingProject}
        onClose={() => {
          setShowCreateForm(false);
          setEditingProject(null);
        }}
        project={editingProject}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
};

export default ProjectsGrid;
