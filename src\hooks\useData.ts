/**
 * Zenith Pulse Manager - Data Management Hooks
 * خطافات إدارة البيانات لتطبيق Zenith Pulse Manager
 * 
 * React hooks for data management with caching and real-time updates
 * Based on 2024-2025 best practices for React data fetching
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  TaskService, 
  ProjectService, 
  NoteService, 
  AnalyticsService, 
  SearchService, 
  SettingsService 
} from '@/services/dataService';
import { Task, Project, Note, AnalyticsData, UserSettings } from '@/lib/database';

// ===== GENERIC DATA HOOK =====

interface UseDataOptions {
  refreshInterval?: number;
  enabled?: boolean;
}

function useData<T>(
  fetchFn: () => Promise<T>,
  deps: React.DependencyList = [],
  options: UseDataOptions = {}
) {
  const { refreshInterval, enabled = true } = options;
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;
    
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [fetchFn, enabled]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...deps]);

  useEffect(() => {
    if (refreshInterval && enabled) {
      const interval = setInterval(fetchData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchData, refreshInterval, enabled]);

  const refetch = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}

// ===== TASK HOOKS =====

export function useTasks(options?: UseDataOptions) {
  return useData(TaskService.getAllTasks, [], options);
}

export function useTask(id: string, options?: UseDataOptions) {
  return useData(
    () => TaskService.getTaskById(id),
    [id],
    options
  );
}

export function useTasksByStatus(status: Task['status'], options?: UseDataOptions) {
  return useData(
    () => TaskService.getTasksByStatus(status),
    [status],
    options
  );
}

export function useTasksByPriority(priority: Task['priority'], options?: UseDataOptions) {
  return useData(
    () => TaskService.getTasksByPriority(priority),
    [priority],
    options
  );
}

export function useTodayTasks(options?: UseDataOptions) {
  return useData(TaskService.getTodayTasks, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 60000 // Refresh every minute
  });
}

export function useTaskStats(options?: UseDataOptions) {
  return useData(TaskService.getTaskStats, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 30000 // Refresh every 30 seconds
  });
}

export function useTaskMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createTask = useCallback(async (taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);
      setError(null);
      const task = await TaskService.createTask(taskData);
      return task;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create task');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTask = useCallback(async (id: string, updates: Partial<Task>) => {
    try {
      setLoading(true);
      setError(null);
      const task = await TaskService.updateTask(id, updates);
      return task;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update task');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTask = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await TaskService.deleteTask(id);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete task');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createTask,
    updateTask,
    deleteTask,
    loading,
    error
  };
}

// ===== PROJECT HOOKS =====

export function useProjects(options?: UseDataOptions) {
  return useData(ProjectService.getAllProjects, [], options);
}

export function useProject(id: string, options?: UseDataOptions) {
  return useData(
    () => ProjectService.getProjectById(id),
    [id],
    options
  );
}

export function useProjectsByStatus(status: Project['status'], options?: UseDataOptions) {
  return useData(
    () => ProjectService.getProjectsByStatus(status),
    [status],
    options
  );
}

export function useProjectStats(options?: UseDataOptions) {
  return useData(ProjectService.getProjectStats, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 30000
  });
}

export function useProjectMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createProject = useCallback(async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt' | 'tasks' | 'team'>) => {
    try {
      setLoading(true);
      setError(null);
      const project = await ProjectService.createProject(projectData);
      return project;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create project');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProject = useCallback(async (id: string, updates: Partial<Project>) => {
    try {
      setLoading(true);
      setError(null);
      const project = await ProjectService.updateProject(id, updates);
      return project;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update project');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProject = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await ProjectService.deleteProject(id);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete project');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const addTaskToProject = useCallback(async (projectId: string, taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await ProjectService.addTaskToProject(projectId, taskId);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to add task to project');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const removeTaskFromProject = useCallback(async (projectId: string, taskId: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await ProjectService.removeTaskFromProject(projectId, taskId);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to remove task from project');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createProject,
    updateProject,
    deleteProject,
    addTaskToProject,
    removeTaskFromProject,
    loading,
    error
  };
}

// ===== NOTE HOOKS =====

export function useNotes(options?: UseDataOptions) {
  return useData(NoteService.getAllNotes, [], options);
}

export function useNote(id: string, options?: UseDataOptions) {
  return useData(
    () => NoteService.getNoteById(id),
    [id],
    options
  );
}

export function usePinnedNotes(options?: UseDataOptions) {
  return useData(NoteService.getPinnedNotes, [], options);
}

export function useNoteStats(options?: UseDataOptions) {
  return useData(NoteService.getNoteStats, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 30000
  });
}

export function useNoteMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createNote = useCallback(async (noteData: Omit<Note, 'id' | 'createdAt' | 'updatedAt' | 'lastModified' | 'wordCount'>) => {
    try {
      setLoading(true);
      setError(null);
      const note = await NoteService.createNote(noteData);
      return note;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create note');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateNote = useCallback(async (id: string, updates: Partial<Note>) => {
    try {
      setLoading(true);
      setError(null);
      const note = await NoteService.updateNote(id, updates);
      return note;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update note');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteNote = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await NoteService.deleteNote(id);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete note');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const togglePin = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await NoteService.togglePin(id);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to toggle pin');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const archiveNote = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const success = await NoteService.archiveNote(id);
      return success;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to archive note');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createNote,
    updateNote,
    deleteNote,
    togglePin,
    archiveNote,
    loading,
    error
  };
}

// ===== ANALYTICS HOOKS =====

export function useTodayAnalytics(options?: UseDataOptions) {
  return useData(AnalyticsService.getTodayAnalytics, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 60000 // Refresh every minute
  });
}

export function useWeeklyAnalytics(options?: UseDataOptions) {
  return useData(AnalyticsService.getWeeklyAnalytics, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 5 * 60000 // Refresh every 5 minutes
  });
}

export function useMonthlyAnalytics(options?: UseDataOptions) {
  return useData(AnalyticsService.getMonthlyAnalytics, [], {
    ...options,
    refreshInterval: options?.refreshInterval || 10 * 60000 // Refresh every 10 minutes
  });
}

export function useAnalyticsRange(startDate: Date, endDate: Date, options?: UseDataOptions) {
  return useData(
    () => AnalyticsService.getAnalyticsRange(startDate, endDate),
    [startDate.toISOString(), endDate.toISOString()], // Pass ISO strings for cache key, but Date objects to service
    options
  );
}

export function useAnalyticsMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateFocusTime = useCallback(async (minutes: number) => {
    try {
      setLoading(true);
      setError(null);
      await AnalyticsService.updateFocusTime(minutes);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update focus time');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    updateFocusTime,
    loading,
    error
  };
}

// ===== SEARCH HOOKS =====

export function useSearch(query: string, type?: 'task' | 'project' | 'note', options?: UseDataOptions) {
  const [debouncedQuery, setDebouncedQuery] = useState(query);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  return useData(
    () => SearchService.search(debouncedQuery, type),
    [debouncedQuery, type],
    {
      ...options,
      enabled: (options?.enabled !== false) && debouncedQuery.length > 0
    }
  );
}

export function useRecentSearches() {
  const [searches, setSearches] = useState<string[]>([]);

  useEffect(() => {
    SearchService.getRecentSearches().then(setSearches);
  }, []);

  const addRecentSearch = useCallback(async (query: string) => {
    await SearchService.addRecentSearch(query);
    const updated = await SearchService.getRecentSearches();
    setSearches(updated);
  }, []);

  return {
    searches,
    addRecentSearch
  };
}

// ===== SETTINGS HOOKS =====

export function useSettings(options?: UseDataOptions) {
  return useData(SettingsService.getSettings, [], options);
}

export function useSettingsMutations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateSettings = useCallback(async (updates: Partial<UserSettings>) => {
    try {
      setLoading(true);
      setError(null);
      const settings = await SettingsService.updateSettings(updates);
      return settings;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update settings');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const resetSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const settings = await SettingsService.resetSettings();
      return settings;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to reset settings');
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    updateSettings,
    resetSettings,
    loading,
    error
  };
}

// ===== COMBINED DASHBOARD HOOK =====

export function useDashboardData(options?: UseDataOptions) {
  const { data: tasks, loading: tasksLoading, error: tasksError } = useTasks(options);
  const { data: projects, loading: projectsLoading, error: projectsError } = useProjects(options);
  const { data: notes, loading: notesLoading, error: notesError } = useNotes(options);
  const { data: todayAnalytics, loading: analyticsLoading, error: analyticsError } = useTodayAnalytics(options);
  const { data: taskStats, loading: taskStatsLoading, error: taskStatsError } = useTaskStats(options);
  const { data: projectStats, loading: projectStatsLoading, error: projectStatsError } = useProjectStats(options);

  const loading = tasksLoading || projectsLoading || notesLoading || analyticsLoading || taskStatsLoading || projectStatsLoading;
  const error = tasksError || projectsError || notesError || analyticsError || taskStatsError || projectStatsError;

  const dashboardData = useMemo(() => {
    if (!tasks || !projects || !notes || !taskStats || !projectStats) {
      return null;
    }

    return {
      tasks,
      projects,
      notes,
      todayAnalytics,
      taskStats,
      projectStats,
      summary: {
        totalTasks: taskStats.total,
        completedTasks: taskStats.completed,
        totalProjects: projectStats.total,
        activeProjects: projectStats.active,
        totalNotes: notes.length,
        productivityScore: todayAnalytics?.productivityScore || 0
      }
    };
  }, [tasks, projects, notes, todayAnalytics, taskStats, projectStats]);

  return {
    data: dashboardData,
    loading,
    error
  };
}
