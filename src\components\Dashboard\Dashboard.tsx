
import React from 'react';
import { CheckSquare, Calendar, FileText, TrendingUp, Loader2, AlertCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDashboardData } from '@/hooks/useData';
import WelcomeCard from './WelcomeCard';
import StatsCard from './StatsCard';
import TaskWidget from './TaskWidget';
import ProductivityChart from './ProductivityChart';
import QuickActions from './QuickActions';

const Dashboard: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { data: dashboardData, loading, error } = useDashboardData();

  if (loading) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">{t('common.loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center py-12 text-destructive">
          <AlertCircle className="w-8 h-8 mr-2" />
          <span>{t('common.error')}: {error.message}</span>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return null;
  }

  const { summary, todayAnalytics } = dashboardData;

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      {/* Welcome Section */}
      <div className="grid grid-cols-1 gap-6">
        <WelcomeCard />
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title={isRTL ? "المهام المكتملة" : "Completed Tasks"}
          value={summary.completedTasks.toString()}
          subtitle={isRTL ? "اليوم" : "Today"}
          icon={CheckSquare}
          color="primary"
          progress={summary.totalTasks > 0 ? Math.round((summary.completedTasks / summary.totalTasks) * 100) : 0}
          trend="up"
          trendValue={`${summary.completedTasks}/${summary.totalTasks}`}
        />
        <StatsCard
          title={isRTL ? "المشاريع النشطة" : "Active Projects"}
          value={summary.activeProjects.toString()}
          subtitle={isRTL ? "قيد التنفيذ" : "In Progress"}
          icon={Calendar}
          color="secondary"
          progress={summary.totalProjects > 0 ? Math.round((summary.activeProjects / summary.totalProjects) * 100) : 0}
          trend="up"
          trendValue={`${summary.activeProjects}/${summary.totalProjects}`}
        />
        <StatsCard
          title={isRTL ? "الملاحظات" : "Notes"}
          value={summary.totalNotes.toString()}
          subtitle={isRTL ? "إجمالي" : "Total"}
          icon={FileText}
          color="success"
          trend="up"
          trendValue={summary.totalNotes.toString()}
        />
        <StatsCard
          title={isRTL ? "نقاط الإنتاجية" : "Productivity Score"}
          value={`${Math.round(summary.productivityScore)}%`}
          subtitle={isRTL ? "اليوم" : "Today"}
          icon={TrendingUp}
          color="neutral"
          progress={Math.round(summary.productivityScore)}
          trend="up"
          trendValue={`${Math.round(summary.productivityScore)}%`}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Tasks */}
        <div className="lg:col-span-2 space-y-6">
          <TaskWidget />
          <ProductivityChart />
        </div>

        {/* Right Column - Quick Actions */}
        <div className="space-y-6">
          <QuickActions />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
