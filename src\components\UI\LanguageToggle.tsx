
import React from 'react';
import { Globe } from 'lucide-react';
import { useLanguage, Language } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const LanguageToggle: React.FC = () => {
  const { language, setLanguage, t } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar');
  };

  return (
    <button
      onClick={toggleLanguage}
      className={cn(
        "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200",
        "bg-card hover:bg-accent text-foreground hover:text-accent-foreground",
        "border border-border hover:border-accent-foreground/20",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      )}
      aria-label={t('settings.language')}
    >
      <Globe className="w-4 h-4" />
      <span className="text-sm font-medium">
        {language === 'ar' ? 'عربي' : 'English'}
      </span>
    </button>
  );
};

export default LanguageToggle;
