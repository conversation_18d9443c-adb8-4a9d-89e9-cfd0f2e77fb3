/**
 * Zenith Pulse Manager - Project Form Component
 * مكون نموذج المشاريع لتطبيق Zenith Pulse Manager
 * 
 * Complete project creation and editing form with validation
 */

import React, { useState, useEffect } from 'react';
import { X, Calendar, Users, AlertCircle, Loader2, Plus, Trash2 } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useProjectMutations } from '@/hooks/useData';
import { Project, ProjectMember } from '@/lib/database';

interface ProjectFormProps {
  isOpen: boolean;
  onClose: () => void;
  project?: Project | null;
  onSuccess?: () => void;
}

const ProjectForm: React.FC<ProjectFormProps> = ({ isOpen, onClose, project, onSuccess }) => {
  const { t, isRTL } = useLanguage();
  const { createProject, updateProject, loading } = useProjectMutations();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'active' as Project['status'],
    progress: 0,
    deadline: '',
    color: 'from-blue-500 to-blue-600',
    budget: '',
  });

  const [teamMembers, setTeamMembers] = useState<ProjectMember[]>([]);
  const [newMember, setNewMember] = useState({
    name: '',
    role: '',
    email: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const colorOptions = [
    { value: 'from-blue-500 to-blue-600', label: 'Blue', color: 'bg-blue-500' },
    { value: 'from-green-500 to-green-600', label: 'Green', color: 'bg-green-500' },
    { value: 'from-purple-500 to-purple-600', label: 'Purple', color: 'bg-purple-500' },
    { value: 'from-red-500 to-red-600', label: 'Red', color: 'bg-red-500' },
    { value: 'from-yellow-500 to-yellow-600', label: 'Yellow', color: 'bg-yellow-500' },
    { value: 'from-indigo-500 to-indigo-600', label: 'Indigo', color: 'bg-indigo-500' },
  ];

  // Initialize form data when editing
  useEffect(() => {
    if (project) {
      setFormData({
        title: project.title,
        description: project.description,
        status: project.status,
        progress: project.progress,
        deadline: project.deadline || '',
        color: project.color,
        budget: project.budget?.toString() || '',
      });
      setTeamMembers(project.team || []);
    } else {
      // Reset form for new project
      setFormData({
        title: '',
        description: '',
        status: 'active',
        progress: 0,
        deadline: '',
        color: 'from-blue-500 to-blue-600',
        budget: '',
      });
      setTeamMembers([]);
    }
    setNewMember({ name: '', role: '', email: '' });
    setErrors({});
  }, [project, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t('projects.validation.titleRequired');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('projects.validation.descriptionRequired');
    }

    if (formData.progress < 0 || formData.progress > 100) {
      newErrors.progress = t('projects.validation.progressInvalid');
    }

    if (formData.deadline && new Date(formData.deadline) < new Date()) {
      newErrors.deadline = t('projects.validation.deadlinePast');
    }

    if (formData.budget && (isNaN(Number(formData.budget)) || Number(formData.budget) <= 0)) {
      newErrors.budget = t('projects.validation.budgetInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      const projectData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        status: formData.status,
        progress: formData.progress,
        deadline: formData.deadline || undefined,
        color: formData.color,
        budget: formData.budget ? Number(formData.budget) : undefined,
        team: teamMembers,
      };

      if (project) {
        await updateProject(project.id, projectData);
      } else {
        await createProject(projectData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Failed to save project:', error);
    }
  };

  const handleAddMember = () => {
    if (newMember.name.trim() && newMember.role.trim()) {
      const member: ProjectMember = {
        id: Date.now().toString(),
        name: newMember.name.trim(),
        role: newMember.role.trim(),
        email: newMember.email.trim() || undefined,
      };
      setTeamMembers(prev => [...prev, member]);
      setNewMember({ name: '', role: '', email: '' });
    }
  };

  const handleRemoveMember = (memberId: string) => {
    setTeamMembers(prev => prev.filter(member => member.id !== memberId));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-card border border-border rounded-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold">
            {project ? t('projects.editProject') : t('projects.createProject')}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('projects.title')} *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className={cn(
                "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary",
                errors.title ? "border-destructive" : "border-border",
                isRTL && "text-right"
              )}
              placeholder={t('projects.titlePlaceholder')}
            />
            {errors.title && (
              <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                <AlertCircle className="w-4 h-4" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('projects.description')} *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              className={cn(
                "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none",
                errors.description ? "border-destructive" : "border-border",
                isRTL && "text-right"
              )}
              placeholder={t('projects.descriptionPlaceholder')}
            />
            {errors.description && (
              <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                <AlertCircle className="w-4 h-4" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Status and Progress */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('projects.status')}
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as Project['status'] }))}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="active">{t('projects.status.active')}</option>
                <option value="pending">{t('projects.status.pending')}</option>
                <option value="completed">{t('projects.status.completed')}</option>
                <option value="cancelled">{t('projects.status.cancelled')}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('projects.progress')} ({formData.progress}%)
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={formData.progress}
                onChange={(e) => setFormData(prev => ({ ...prev, progress: Number(e.target.value) }))}
                className="w-full"
              />
              {errors.progress && (
                <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="w-4 h-4" />
                  {errors.progress}
                </p>
              )}
            </div>
          </div>

          {/* Deadline and Budget */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('projects.deadline')}
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={formData.deadline}
                  onChange={(e) => setFormData(prev => ({ ...prev, deadline: e.target.value }))}
                  className={cn(
                    "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary",
                    errors.deadline ? "border-destructive" : "border-border"
                  )}
                />
                <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
              </div>
              {errors.deadline && (
                <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="w-4 h-4" />
                  {errors.deadline}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('projects.budget')}
              </label>
              <input
                type="number"
                min="0"
                step="100"
                value={formData.budget}
                onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                className={cn(
                  "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary",
                  errors.budget ? "border-destructive" : "border-border"
                )}
                placeholder="0"
              />
              {errors.budget && (
                <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                  <AlertCircle className="w-4 h-4" />
                  {errors.budget}
                </p>
              )}
            </div>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('projects.color')}
            </label>
            <div className="grid grid-cols-6 gap-2">
              {colorOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, color: option.value }))}
                  className={cn(
                    "w-12 h-12 rounded-lg border-2 transition-all",
                    option.color,
                    formData.color === option.value
                      ? "border-primary scale-110"
                      : "border-border hover:border-primary/50"
                  )}
                  title={option.label}
                />
              ))}
            </div>
          </div>

          {/* Team Members */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('projects.teamMembers')}
            </label>
            
            {/* Add Member Form */}
            <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <input
                  type="text"
                  value={newMember.name}
                  onChange={(e) => setNewMember(prev => ({ ...prev, name: e.target.value }))}
                  placeholder={t('projects.memberName')}
                  className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <input
                  type="text"
                  value={newMember.role}
                  onChange={(e) => setNewMember(prev => ({ ...prev, role: e.target.value }))}
                  placeholder={t('projects.memberRole')}
                  className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <input
                  type="email"
                  value={newMember.email}
                  onChange={(e) => setNewMember(prev => ({ ...prev, email: e.target.value }))}
                  placeholder={t('projects.memberEmail')}
                  className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              <button
                type="button"
                onClick={handleAddMember}
                className="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                <Plus className="w-4 h-4" />
                {t('projects.addMember')}
              </button>
            </div>

            {/* Team Members List */}
            {teamMembers.length > 0 && (
              <div className="mt-4 space-y-2">
                {teamMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-3 bg-card border border-border rounded-lg"
                  >
                    <div>
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-muted-foreground">{member.role}</div>
                      {member.email && (
                        <div className="text-xs text-muted-foreground">{member.email}</div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveMember(member.id)}
                      className="p-2 hover:bg-destructive/10 text-destructive rounded-lg transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className={cn(
            "flex gap-3 pt-4 border-t border-border",
            isRTL && "flex-row-reverse"
          )}>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin" />}
              {project ? t('common.update') : t('common.create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectForm;
