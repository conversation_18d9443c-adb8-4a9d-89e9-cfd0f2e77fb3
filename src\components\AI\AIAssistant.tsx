
/**
 * Zenith Pulse Manager - AI Assistant Component
 * مكون المساعد الذكي لتطبيق Zenith Pulse Manager
 *
 * Interactive AI assistant with Gemini Flash 2.5 integration
 */

import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Loader2, <PERSON><PERSON><PERSON>, X, Minimize2, Maximize2, MessageCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { aiService, AIResponse } from '@/services/aiService';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface AIAssistantProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

const AIAssistant: React.FC<AIAssistantProps> = ({
  isOpen: propIsOpen,
  onClose: propOnClose,
  className = ''
}) => {
  const { t, isRTL } = useLanguage();
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use prop values if provided, otherwise use internal state
  const isOpen = propIsOpen !== undefined ? propIsOpen : internalIsOpen;
  const onClose = propOnClose || (() => setInternalIsOpen(false));

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus();
    }
  }, [isOpen, isMinimized]);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: isRTL
          ? 'مرحباً! أنا Zenith AI، مساعدك الذكي للإنتاجية. كيف يمكنني مساعدتك اليوم؟'
          : 'Hello! I\'m Zenith AI, your intelligent productivity assistant. How can I help you today?',
        timestamp: new Date(),
        suggestions: [
          isRTL ? 'تحليل إنتاجيتي' : 'Analyze my productivity',
          isRTL ? 'اقتراح مهام جديدة' : 'Suggest new tasks',
          isRTL ? 'مساعدة في إدارة المشاريع' : 'Help with project management',
        ]
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, messages.length, isRTL]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const response: AIResponse = await aiService.processQuery(userMessage.content);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: response.message,
        timestamp: new Date(),
        suggestions: response.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: isRTL
          ? 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.'
          : 'Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleQuickAction = async (action: string) => {
    setIsLoading(true);
    try {
      let response: AIResponse;

      switch (action) {
        case 'analyze':
          response = await aiService.analyzeProductivity();
          break;
        case 'suggest':
          response = await aiService.getTaskSuggestions();
          break;
        default:
          response = await aiService.processQuery(action);
      }

      const aiMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: response.message,
        timestamp: new Date(),
        suggestions: response.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Quick action failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* AI Assistant Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-zenith-gradient text-white rounded-full shadow-zenith-lg hover:shadow-zenith-lg hover:scale-110 transition-all duration-200 flex items-center justify-center zenith-glow"
        title={t('ai.title')}
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      {/* AI Assistant Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-end justify-end p-6">
          <div className="w-96 h-[600px] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-2xl flex flex-col animate-scale-in transition-colors duration-200">
            {/* Header */}
            <div className={cn(
              "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-zenith-gradient text-white rounded-t-2xl",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
                <MessageCircle className="w-5 h-5" />
                <h3 className="font-semibold">{t('ai.title')}</h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <Lightbulb className="w-12 h-12 mx-auto mb-4 text-zenith-sage-400" />
                  <p className="mb-4">{t('ai.suggestions')}</p>
                  <div className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion.action)}
                        className={cn(
                          "w-full flex items-center gap-3 p-3 rounded-lg bg-muted hover:bg-muted/80 transition-colors text-left",
                          isRTL && "flex-row-reverse text-right"
                        )}
                      >
                        <suggestion.icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm">{suggestion.text}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-3",
                    message.isUser && (isRTL ? "flex-row-reverse" : "flex-row-reverse")
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] p-3 rounded-2xl",
                      message.isUser
                        ? "bg-zenith-gradient text-white ml-auto"
                        : "bg-accent text-foreground mr-auto"
                    )}
                  >
                    <p className="text-sm leading-relaxed">{message.text}</p>
                  </div>
                </div>
              ))}

              {isThinking && (
                <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
                  <div className="bg-accent text-foreground p-3 rounded-2xl mr-auto">
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-sm text-muted-foreground">{t('ai.thinking')}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div className="p-4 border-t border-border">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder={t('ai.placeholder')}
                  className={cn(
                    "flex-1 bg-accent border border-border rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                    isRTL && "text-right"
                  )}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || isThinking}
                  className="p-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AIAssistant;
