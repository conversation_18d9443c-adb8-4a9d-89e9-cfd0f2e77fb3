/**
 * Zenith Pulse Manager - Note Form Component
 * مكون نموذج الملاحظات لتطبيق Zenith Pulse Manager
 * 
 * Complete note creation and editing form with validation
 */

import React, { useState, useEffect } from 'react';
import { X, Tag, AlertCircle, Loader2, Pin } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useNoteMutations } from '@/hooks/useData';
import { Note } from '@/lib/database';

interface NoteFormProps {
  isOpen: boolean;
  onClose: () => void;
  note?: Note | null;
  onSuccess?: () => void;
}

const NoteForm: React.FC<NoteFormProps> = ({ isOpen, onClose, note, onSuccess }) => {
  const { t, isRTL } = useLanguage();
  const { createNote, updateNote, loading } = useNoteMutations();

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    tags: [] as string[],
    color: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20',
    isPinned: false,
    isArchived: false,
  });

  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const colorOptions = [
    { 
      value: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20', 
      label: 'Blue', 
      preview: 'bg-blue-100 dark:bg-blue-900/20' 
    },
    { 
      value: 'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20', 
      label: 'Green', 
      preview: 'bg-green-100 dark:bg-green-900/20' 
    },
    { 
      value: 'from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20', 
      label: 'Purple', 
      preview: 'bg-purple-100 dark:bg-purple-900/20' 
    },
    { 
      value: 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20', 
      label: 'Red', 
      preview: 'bg-red-100 dark:bg-red-900/20' 
    },
    { 
      value: 'from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20', 
      label: 'Yellow', 
      preview: 'bg-yellow-100 dark:bg-yellow-900/20' 
    },
    { 
      value: 'from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20', 
      label: 'Indigo', 
      preview: 'bg-indigo-100 dark:bg-indigo-900/20' 
    },
  ];

  // Initialize form data when editing
  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title,
        content: note.content,
        tags: note.tags || [],
        color: note.color,
        isPinned: note.isPinned,
        isArchived: note.isArchived,
      });
    } else {
      // Reset form for new note
      setFormData({
        title: '',
        content: '',
        tags: [],
        color: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20',
        isPinned: false,
        isArchived: false,
      });
    }
    setTagInput('');
    setErrors({});
  }, [note, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = t('notes.validation.titleRequired');
    }

    if (!formData.content.trim()) {
      newErrors.content = t('notes.validation.contentRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      const noteData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        tags: formData.tags,
        color: formData.color,
        isPinned: formData.isPinned,
        isArchived: formData.isArchived,
      };

      if (note) {
        await updateNote(note.id, noteData);
      } else {
        await createNote(noteData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  };

  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.target === document.querySelector('input[name="tagInput"]')) {
      e.preventDefault();
      handleAddTag();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-card border border-border rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold">
            {note ? t('notes.editNote') : t('notes.createNote')}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('notes.title')} *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className={cn(
                "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary",
                errors.title ? "border-destructive" : "border-border",
                isRTL && "text-right"
              )}
              placeholder={t('notes.titlePlaceholder')}
            />
            {errors.title && (
              <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                <AlertCircle className="w-4 h-4" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('notes.content')} *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              rows={8}
              className={cn(
                "w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none",
                errors.content ? "border-destructive" : "border-border",
                isRTL && "text-right"
              )}
              placeholder={t('notes.contentPlaceholder')}
            />
            {errors.content && (
              <p className="text-destructive text-sm mt-1 flex items-center gap-1">
                <AlertCircle className="w-4 h-4" />
                {errors.content}
              </p>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              {t('notes.wordCount')}: {formData.content.split(/\s+/).filter(word => word.length > 0).length}
            </div>
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('notes.color')}
            </label>
            <div className="grid grid-cols-6 gap-2">
              {colorOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, color: option.value }))}
                  className={cn(
                    "w-12 h-12 rounded-lg border-2 transition-all",
                    option.preview,
                    formData.color === option.value
                      ? "border-primary scale-110"
                      : "border-border hover:border-primary/50"
                  )}
                  title={option.label}
                />
              ))}
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('notes.tags')}
            </label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <input
                  type="text"
                  name="tagInput"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className={cn(
                    "flex-1 px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary",
                    isRTL && "text-right"
                  )}
                  placeholder={t('notes.addTag')}
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 bg-muted hover:bg-muted/80 rounded-lg transition-colors"
                >
                  <Tag className="w-4 h-4" />
                </button>
              </div>
              
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-full text-sm"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="hover:bg-primary/20 rounded-full p-0.5"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="isPinned"
                checked={formData.isPinned}
                onChange={(e) => setFormData(prev => ({ ...prev, isPinned: e.target.checked }))}
                className="w-4 h-4 text-primary focus:ring-primary border-border rounded"
              />
              <label htmlFor="isPinned" className="flex items-center gap-2 text-sm font-medium cursor-pointer">
                <Pin className="w-4 h-4" />
                {t('notes.pinNote')}
              </label>
            </div>

            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="isArchived"
                checked={formData.isArchived}
                onChange={(e) => setFormData(prev => ({ ...prev, isArchived: e.target.checked }))}
                className="w-4 h-4 text-primary focus:ring-primary border-border rounded"
              />
              <label htmlFor="isArchived" className="text-sm font-medium cursor-pointer">
                {t('notes.archiveNote')}
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className={cn(
            "flex gap-3 pt-4 border-t border-border",
            isRTL && "flex-row-reverse"
          )}>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin" />}
              {note ? t('common.update') : t('common.create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NoteForm;
