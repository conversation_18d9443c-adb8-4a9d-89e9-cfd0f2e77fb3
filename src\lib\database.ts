/**
 * Zenith Pulse Manager - IndexedDB Database Schema
 * نظام قاعدة البيانات المحلية لتطبيق Zenith Pulse Manager
 * 
 * Modern IndexedDB implementation with Dexie for offline-first functionality
 * Based on 2024-2025 best practices for web application data storage
 */

import Dexie, { Table } from 'dexie';
import { v4 as uuidv4 } from 'uuid';

// ===== TYPE DEFINITIONS =====

export interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'todo' | 'inProgress' | 'completed' | 'cancelled';
  dueDate?: Date;
  tags: string[];
  projectId?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  estimatedHours?: number;
  actualHours?: number;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'pending' | 'cancelled';
  progress: number;
  team: ProjectMember[];
  tasks: string[]; // Task IDs
  deadline?: Date;
  color: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  budget?: number;
  actualCost?: number;
}

export interface ProjectMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  email?: string;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  tags: string[];
  color: string;
  wordCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastModified: Date;
  isPinned: boolean;
  isArchived: boolean;
  attachments?: string[];
}

export interface AnalyticsData {
  id: string;
  date: Date;
  tasksCompleted: number;
  tasksCreated: number;
  projectsActive: number;
  projectsCompleted: number;
  notesCreated: number;
  focusTimeMinutes: number;
  productivityScore: number;
  createdAt: Date;
}

export interface UserSettings {
  id: string;
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'ar';
  notifications: {
    taskReminders: boolean;
    projectDeadlines: boolean;
    dailySummary: boolean;
  };
  focusMode: {
    defaultDuration: number;
    breakDuration: number;
    soundEnabled: boolean;
  };
  productivity: {
    workingHours: {
      start: string;
      end: string;
    };
    weeklyGoal: number;
  };
  updatedAt: Date;
}

export interface SearchIndex {
  id: string;
  type: 'task' | 'project' | 'note';
  entityId: string;
  title: string;
  content: string;
  tags: string[];
  searchText: string; // Combined searchable text
  updatedAt: Date;
}

// ===== DATABASE CLASS =====

export class ZenithDatabase extends Dexie {
  // Tables
  tasks!: Table<Task>;
  projects!: Table<Project>;
  notes!: Table<Note>;
  analytics!: Table<AnalyticsData>;
  settings!: Table<UserSettings>;
  searchIndex!: Table<SearchIndex>;

  constructor() {
    super('ZenithPulseManager');
    
    this.version(1).stores({
      tasks: 'id, title, priority, status, dueDate, projectId, createdAt, updatedAt, *tags',
      projects: 'id, title, status, progress, deadline, createdAt, updatedAt, *tasks',
      notes: 'id, title, createdAt, updatedAt, lastModified, isPinned, isArchived, *tags',
      analytics: 'id, date, tasksCompleted, projectsActive, productivityScore, createdAt',
      settings: 'id',
      searchIndex: 'id, type, entityId, *tags, searchText'
    });

    // Hooks for automatic timestamps and search indexing
    this.tasks.hook('creating', (primKey, obj, trans) => {
      obj.id = obj.id || uuidv4();
      obj.createdAt = new Date();
      obj.updatedAt = new Date();
    });

    this.tasks.hook('updating', (modifications, primKey, obj, trans) => {
      const taskModifications = modifications as Partial<Task>;
      const currentTask = obj as Task;
      taskModifications.updatedAt = new Date();
      if (taskModifications.status === 'completed' && !currentTask.completedAt) {
        taskModifications.completedAt = new Date();
      }
    });

    this.projects.hook('creating', (_primKey, obj, _trans) => {
      obj.id = obj.id || uuidv4();
      obj.createdAt = new Date();
      obj.updatedAt = new Date();
      obj.tasks = obj.tasks || [];
      obj.team = obj.team || [];
    });

    this.projects.hook('updating', (modifications, _primKey, obj, _trans) => {
      const projectModifications = modifications as Partial<Project>;
      const currentProject = obj as Project;
      projectModifications.updatedAt = new Date();
      if (projectModifications.status === 'completed' && !currentProject.completedAt) {
        projectModifications.completedAt = new Date();
      }
    });

    this.notes.hook('creating', (_primKey, obj, _trans) => {
      obj.id = obj.id || uuidv4();
      obj.createdAt = new Date();
      obj.updatedAt = new Date();
      obj.lastModified = new Date();
      obj.wordCount = obj.content ? obj.content.split(/\s+/).length : 0;
      obj.isPinned = obj.isPinned || false;
      obj.isArchived = obj.isArchived || false;
    });

    this.notes.hook('updating', (modifications, _primKey, _obj, _trans) => {
      const noteModifications = modifications as Partial<Note>;
      noteModifications.updatedAt = new Date();
      noteModifications.lastModified = new Date();
      if (noteModifications.content) {
        noteModifications.wordCount = noteModifications.content.split(/\s+/).length;
      }
    });

    this.analytics.hook('creating', (_primKey, obj, _trans) => {
      obj.id = obj.id || uuidv4();
      obj.date = obj.date || new Date(); // Ensure date is a Date object
      obj.createdAt = new Date();
    });

    this.settings.hook('creating', (_primKey, obj, _trans) => {
      obj.id = obj.id || 'user-settings';
      obj.updatedAt = new Date();
    });

    this.settings.hook('updating', (modifications, _primKey, _obj, _trans) => {
      const settingsModifications = modifications as Partial<UserSettings>;
      settingsModifications.updatedAt = new Date();
    });

    // Search index hooks
    this.tasks.hook('creating', (_primKey, obj, _trans) => {
      this.updateSearchIndex('task', obj);
    });

    this.tasks.hook('updating', (modifications, _primKey, obj, _trans) => {
      const updatedObj = { ...obj, ...modifications };
      this.updateSearchIndex('task', updatedObj);
    });

    this.projects.hook('creating', (_primKey, obj, _trans) => {
      this.updateSearchIndex('project', obj);
    });

    this.projects.hook('updating', (modifications, _primKey, obj, _trans) => {
      const updatedObj = { ...obj, ...modifications };
      this.updateSearchIndex('project', updatedObj);
    });

    this.notes.hook('creating', (_primKey, obj, _trans) => {
      this.updateSearchIndex('note', obj);
    });

    this.notes.hook('updating', (modifications, _primKey, obj, _trans) => {
      const updatedObj = { ...obj, ...modifications };
      this.updateSearchIndex('note', updatedObj);
    });
  }

  // Search index management
  private async updateSearchIndex(type: 'task' | 'project' | 'note', obj: Task | Project | Note) {
    let description = '';
    let content = '';
    let tags: string[] = [];

    if (type === 'task') {
      const task = obj as Task;
      description = task.description || '';
      tags = task.tags || [];
    } else if (type === 'project') {
      const project = obj as Project;
      description = project.description || '';
      tags = [];
    } else if (type === 'note') {
      const note = obj as Note;
      content = note.content || '';
      tags = note.tags || [];
    }

    const searchText = [
      obj.title || '',
      description,
      content,
      ...tags
    ].join(' ').toLowerCase();

    const searchEntry: SearchIndex = {
      id: `${type}-${obj.id}`,
      type,
      entityId: obj.id,
      title: obj.title || '',
      content: description || content,
      tags,
      searchText,
      updatedAt: new Date()
    };

    await this.searchIndex.put(searchEntry);
  }

  // Initialize default settings
  async initializeDefaultSettings(): Promise<void> {
    const existingSettings = await this.settings.get('user-settings');
    if (!existingSettings) {
      const defaultSettings: UserSettings = {
        id: 'user-settings',
        theme: 'system',
        language: 'en',
        notifications: {
          taskReminders: true,
          projectDeadlines: true,
          dailySummary: false
        },
        focusMode: {
          defaultDuration: 25,
          breakDuration: 5,
          soundEnabled: true
        },
        productivity: {
          workingHours: {
            start: '09:00',
            end: '17:00'
          },
          weeklyGoal: 40
        },
        updatedAt: new Date()
      };
      await this.settings.add(defaultSettings);
    }
  }

  // Initialize sample data for development
  async initializeSampleData(): Promise<void> {
    const taskCount = await this.tasks.count();
    if (taskCount === 0) {
      // Add sample tasks
      const sampleTasks: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>[] = [
        {
          title: 'Review monthly reports',
          description: 'Review and analyze last month financial data',
          priority: 'high',
          status: 'todo',
          dueDate: new Date('2024-01-15'),
          tags: ['reports', 'finance']
        },
        {
          title: 'Development team meeting',
          description: 'Discuss new updates and technical issues',
          priority: 'medium',
          status: 'inProgress',
          dueDate: new Date('2024-01-16'),
          tags: ['meeting', 'team']
        }
      ];

      for (const task of sampleTasks) {
        await this.tasks.add(task as Task);
      }
    }
  }
}

// Create and export database instance
export const db = new ZenithDatabase();

// Initialize database
db.open().then(async () => {
  await db.initializeDefaultSettings();
  await db.initializeSampleData();
}).catch(error => {
  console.error('Failed to initialize database:', error);
});
