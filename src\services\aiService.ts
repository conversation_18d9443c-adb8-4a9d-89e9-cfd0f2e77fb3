/**
 * Zenith Pulse Manager - AI Service Integration
 * خدمة الذكاء الاصطناعي لتطبيق Zenith Pulse Manager
 * 
 * Gemini Flash 2.5 API integration for intelligent task and project management
 * Based on 2024-2025 best practices for AI integration
 */

import { TaskService, ProjectService, NoteService, AnalyticsService } from './dataService';
import { Task, Project, Note } from '@/lib/database';

// Gemini API configuration
const GEMINI_API_KEY = 'AIzaSyABKUcX9k3eSFqj10btacTfh1dJOG2zugA';
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';

interface GeminiRequest {
  contents: {
    parts: {
      text: string;
    }[];
  }[];
  generationConfig?: {
    temperature?: number;
    topK?: number;
    topP?: number;
    maxOutputTokens?: number;
  };
}

interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
    finishReason: string;
  }[];
}

// AI Assistant capabilities
export interface AICapabilities {
  canCreateTasks: boolean;
  canUpdateTasks: boolean;
  canCreateProjects: boolean;
  canUpdateProjects: boolean;
  canCreateNotes: boolean;
  canAnalyzeData: boolean;
  canProvideInsights: boolean;
}

export interface AIContext {
  tasks: Task[];
  projects: Project[];
  notes: Note[];
  analytics: any;
  userQuery: string;
  capabilities: AICapabilities;
}

export interface AIResponse {
  message: string;
  actions?: AIAction[];
  suggestions?: string[];
  data?: any;
}

export interface AIAction {
  type: 'create_task' | 'update_task' | 'create_project' | 'update_project' | 'create_note' | 'analyze_data';
  payload: any;
  description: string;
}

class AIService {
  private static instance: AIService;
  private capabilities: AICapabilities = {
    canCreateTasks: true,
    canUpdateTasks: true,
    canCreateProjects: true,
    canUpdateProjects: true,
    canCreateNotes: true,
    canAnalyzeData: true,
    canProvideInsights: true,
  };

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // Make API call to Gemini
  private async callGeminiAPI(prompt: string): Promise<string> {
    try {
      const request: GeminiRequest = {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      };

      const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response from Gemini API');
      }

      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Gemini API call failed:', error);
      throw new Error('Failed to get AI response. Please try again.');
    }
  }

  // Get current application context
  private async getApplicationContext(): Promise<Omit<AIContext, 'userQuery' | 'capabilities'>> {
    try {
      const [tasks, projects, notes, analytics] = await Promise.all([
        TaskService.getAllTasks(),
        ProjectService.getAllProjects(),
        NoteService.getAllNotes(),
        AnalyticsService.getTodayAnalytics(),
      ]);

      return {
        tasks: tasks || [],
        projects: projects || [],
        notes: notes || [],
        analytics,
      };
    } catch (error) {
      console.error('Failed to get application context:', error);
      return {
        tasks: [],
        projects: [],
        notes: [],
        analytics: null,
      };
    }
  }

  // Create system prompt with context
  private createSystemPrompt(context: AIContext): string {
    const { tasks, projects, notes, analytics, capabilities } = context;

    return `You are Zenith AI, an intelligent productivity assistant for the Zenith Pulse Manager application. You help users manage their tasks, projects, and notes efficiently.

CURRENT APPLICATION STATE:
- Tasks: ${tasks.length} total (${tasks.filter(t => t.status === 'completed').length} completed, ${tasks.filter(t => t.status === 'inProgress').length} in progress)
- Projects: ${projects.length} total (${projects.filter(p => p.status === 'active').length} active, ${projects.filter(p => p.status === 'completed').length} completed)
- Notes: ${notes.length} total
- Today's Productivity Score: ${analytics?.productivityScore || 0}%

CAPABILITIES:
${Object.entries(capabilities).map(([key, value]) => `- ${key}: ${value ? 'Yes' : 'No'}`).join('\n')}

RECENT TASKS:
${tasks.slice(0, 5).map(t => `- ${t.title} (${t.status}, priority: ${t.priority})`).join('\n')}

ACTIVE PROJECTS:
${projects.filter(p => p.status === 'active').slice(0, 3).map(p => `- ${p.title} (${p.progress}% complete)`).join('\n')}

INSTRUCTIONS:
1. Provide helpful, actionable responses
2. When suggesting actions, be specific and clear
3. If you can perform actions (create/update tasks, projects, notes), offer to do so
4. Provide productivity insights based on the current data
5. Be concise but informative
6. Support both English and Arabic languages
7. Focus on helping the user be more productive

RESPONSE FORMAT:
- Provide a clear, helpful message
- If suggesting actions, list them clearly
- Include relevant data or insights when appropriate
- Be encouraging and supportive

Remember: You can actually perform actions in the application, so offer to help with specific tasks when appropriate.`;
  }

  // Process user query and generate response
  async processQuery(userQuery: string): Promise<AIResponse> {
    try {
      // Get current application context
      const context = await this.getApplicationContext();
      
      // Create full context
      const fullContext: AIContext = {
        ...context,
        userQuery,
        capabilities: this.capabilities,
      };

      // Create system prompt
      const systemPrompt = this.createSystemPrompt(fullContext);
      
      // Create user prompt
      const userPrompt = `User Query: "${userQuery}"

Please provide a helpful response based on the current application state and your capabilities. If you can help by performing specific actions (like creating tasks, updating projects, etc.), please offer to do so.`;

      // Combine prompts
      const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

      // Get AI response
      const aiMessage = await this.callGeminiAPI(fullPrompt);

      // Parse response and extract potential actions
      const response: AIResponse = {
        message: aiMessage,
        suggestions: this.extractSuggestions(aiMessage),
      };

      return response;
    } catch (error) {
      console.error('Failed to process AI query:', error);
      return {
        message: 'I apologize, but I encountered an error while processing your request. Please try again or rephrase your question.',
        suggestions: [
          'Try asking about your current tasks',
          'Ask for productivity insights',
          'Request help with creating a new task or project',
        ],
      };
    }
  }

  // Extract suggestions from AI response
  private extractSuggestions(message: string): string[] {
    const suggestions: string[] = [];
    
    // Look for common patterns that indicate actionable suggestions
    const patterns = [
      /(?:you could|you might|consider|try|suggest|recommend)\s+([^.!?]+)/gi,
      /(?:would you like to|shall I|I can help you)\s+([^.!?]+)/gi,
    ];

    patterns.forEach(pattern => {
      const matches = message.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const suggestion = match.replace(/^(you could|you might|consider|try|suggest|recommend|would you like to|shall I|I can help you)\s+/i, '').trim();
          if (suggestion.length > 10 && suggestion.length < 100) {
            suggestions.push(suggestion);
          }
        });
      }
    });

    return suggestions.slice(0, 3); // Limit to 3 suggestions
  }

  // Analyze productivity and provide insights
  async analyzeProductivity(): Promise<AIResponse> {
    try {
      const context = await this.getApplicationContext();
      const { tasks, projects, analytics } = context;

      const completedTasks = tasks.filter(t => t.status === 'completed');
      const overdueTasks = tasks.filter(t => 
        t.dueDate && 
        new Date(t.dueDate) < new Date() && 
        t.status !== 'completed'
      );

      const prompt = `Analyze the following productivity data and provide insights:

TASKS ANALYSIS:
- Total tasks: ${tasks.length}
- Completed tasks: ${completedTasks.length}
- Overdue tasks: ${overdueTasks.length}
- Tasks by priority: High: ${tasks.filter(t => t.priority === 'high').length}, Medium: ${tasks.filter(t => t.priority === 'medium').length}, Low: ${tasks.filter(t => t.priority === 'low').length}

PROJECTS ANALYSIS:
- Total projects: ${projects.length}
- Active projects: ${projects.filter(p => p.status === 'active').length}
- Completed projects: ${projects.filter(p => p.status === 'completed').length}
- Average project progress: ${projects.length > 0 ? Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length) : 0}%

TODAY'S METRICS:
- Productivity score: ${analytics?.productivityScore || 0}%
- Tasks completed today: ${analytics?.tasksCompleted || 0}
- Focus time: ${analytics?.focusTimeMinutes || 0} minutes

Please provide:
1. Key insights about productivity patterns
2. Areas for improvement
3. Specific actionable recommendations
4. Encouragement and positive observations

Keep the response concise but insightful.`;

      const aiMessage = await this.callGeminiAPI(prompt);

      return {
        message: aiMessage,
        data: {
          totalTasks: tasks.length,
          completedTasks: completedTasks.length,
          overdueTasks: overdueTasks.length,
          productivityScore: analytics?.productivityScore || 0,
        },
      };
    } catch (error) {
      console.error('Failed to analyze productivity:', error);
      return {
        message: 'I encountered an error while analyzing your productivity data. Please try again.',
      };
    }
  }

  // Get task suggestions based on current workload
  async getTaskSuggestions(): Promise<AIResponse> {
    try {
      const context = await this.getApplicationContext();
      const { tasks, projects } = context;

      const prompt = `Based on the current tasks and projects, suggest new tasks that would be beneficial:

CURRENT TASKS:
${tasks.slice(0, 10).map(t => `- ${t.title} (${t.status}, ${t.priority} priority)`).join('\n')}

ACTIVE PROJECTS:
${projects.filter(p => p.status === 'active').map(p => `- ${p.title} (${p.progress}% complete)`).join('\n')}

Please suggest 3-5 new tasks that would:
1. Help complete existing projects
2. Improve productivity
3. Address any gaps in the current workflow
4. Be realistic and actionable

Format each suggestion as a clear task title with a brief description.`;

      const aiMessage = await this.callGeminiAPI(prompt);

      return {
        message: aiMessage,
        suggestions: [
          'Create suggested tasks',
          'Analyze current workload',
          'Review project priorities',
        ],
      };
    } catch (error) {
      console.error('Failed to get task suggestions:', error);
      return {
        message: 'I encountered an error while generating task suggestions. Please try again.',
      };
    }
  }

  // Get AI capabilities
  getCapabilities(): AICapabilities {
    return { ...this.capabilities };
  }

  // Update AI capabilities
  updateCapabilities(newCapabilities: Partial<AICapabilities>): void {
    this.capabilities = { ...this.capabilities, ...newCapabilities };
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();
export default aiService;
